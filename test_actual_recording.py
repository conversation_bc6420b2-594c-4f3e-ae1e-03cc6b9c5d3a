#!/usr/bin/env python3
import requests
import json
import time

print('🎬 Testing ACTUAL LiveKit Recording (not mock)...')

# Start recording
start_data = {
    'session_id': 'actual-recording-test',
    'teacher_id': 'test-teacher',
    'recording_enabled': True,
    'quality': 'high'
}

print('1. Starting stream with recording...')
response = requests.post('http://localhost:8012/api/enhanced-stream/start', json=start_data)
if response.status_code == 200:
    print('✅ Recording started')
    result = response.json()
    recording_info = result.get('recording', {})
    print(f'   Egress ID: {recording_info.get("egress_id")}')
    print(f'   Status: {recording_info.get("status")}')
else:
    print(f'❌ Failed to start: {response.status_code} - {response.text}')
    exit(1)

# Check detailed status
print('\n2. Checking detailed recording status...')
response = requests.get('http://localhost:8012/api/recording/status/actual-recording-test')
if response.status_code == 200:
    result = response.json()
    print(f'   Recording Status: {result.get("recording_info", {}).get("status")}')
    print(f'   Egress ID: {result.get("recording_info", {}).get("egress_id")}')
    print(f'   S3 Key: {result.get("recording_info", {}).get("s3_key")}')
    egress_status = result.get('egress_status')
    if egress_status:
        print(f'   LiveKit Status: {egress_status.get("status")}')
        if egress_status.get('error'):
            print(f'   LiveKit Error: {egress_status.get("error")}')
else:
    print(f'❌ Failed to get status: {response.status_code}')

# Wait for recording
print('\n3. Recording for 15 seconds...')
time.sleep(15)

# Stop recording
print('\n4. Stopping stream...')
response = requests.post('http://localhost:8012/api/enhanced-stream/stop', json={'session_id': 'actual-recording-test'})
if response.status_code == 200:
    result = response.json()
    print('✅ Recording stopped')
    recording = result.get('recording', {})
    print(f'   Status: {recording.get("status")}')
    print(f'   S3 Key: {recording.get("s3_key")}')
    print(f'   CloudFront URL: {recording.get("cloudfront_url")}')
else:
    print(f'❌ Failed to stop: {response.status_code} - {response.text}')

# Check final status
print('\n5. Checking final status...')
response = requests.get('http://localhost:8012/api/recording/status/actual-recording-test')
if response.status_code == 200:
    result = response.json()
    print(f'   Final Status: {result.get("recording_info", {}).get("status")}')
    print(f'   S3 Exists: {result.get("s3_status", {}).get("exists")}')
    print(f'   File Size: {result.get("s3_status", {}).get("size")} bytes')
    egress_status = result.get('egress_status')
    if egress_status:
        print(f'   LiveKit Final Status: {egress_status.get("status")}')
        if egress_status.get('error'):
            print(f'   LiveKit Error: {egress_status.get("error")}')

# Check S3 files
print('\n6. Checking S3 files...')
response = requests.get('http://localhost:8012/api/recording/s3-files')
if response.status_code == 200:
    result = response.json()
    print(f'Found {result.get("total_count", 0)} files in S3:')
    for file_info in result.get('files', []):
        print(f'  📄 {file_info.get("key")} ({file_info.get("size")} bytes)')
        if 'actual-recording-test' in file_info.get("key", ""):
            print(f'      🎯 This is our test recording!')
else:
    print(f'❌ Failed to list S3 files: {response.status_code}')

print('\n🎉 Test completed!')
