from flask import Flask, request, jsonify

from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room

from flask_cors import CORS

import sys

import os

import json

import uuid

from datetime import datetime, timezone, timedelta

import base64

import asyncio

import threading

import logging  # Added for debugging

from typing import Optional, Dict, Any

# LiveKit imports

from livekit import api, rtc

from livekit.api import AccessToken, VideoGrants, LiveKitAPI

from livekit.rtc import Room, TrackSource

# LiveKit Egress imports for recording

from livekit import api

# AWS imports for S3 and CloudFront

import boto3

from botocore.exceptions import ClientError

# Add parent directory to path to import shared modules

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv

from shared.database import Database  # Import Database

# Import ProxyFix for handling reverse proxy headers

from werkzeug.middleware.proxy_fix import ProxyFix

  

# Configure logging

logging.basicConfig(level=logging.INFO)

logger = logging.getLogger('StreamingApp')

  

load_dotenv()

app = Flask(__name__)

app.config['SECRET_KEY'] = 'enhanced-streaming-secret-key'

  

# Middleware to keep connections alive

@app.before_request

def before_request():

    if request.headers.get('Upgrade') == 'websocket':

        socketio.server.eio.ping_timeout = 900  # 15 minutes

        socketio.server.eio.ping_interval = 20  # 20 seconds

  

# Configure CORS

CORS(app,

     origins="*",

     allow_headers=["Content-Type", "Authorization", "Access-Control-Allow-Credentials"],

     supports_credentials=True,

     methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])

  

# Apply ProxyFix middleware

app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1)

  

# Initialize Socket.IO

socketio = SocketIO(app,

                    cors_allowed_origins="*",

                    cors_credentials=True,

                    logger=True,

                    engineio_logger=True,

                    ping_timeout=900,  # 15 minutes

                    ping_interval=20)  # 20 seconds

  

# Initialize Database

db = Database()

  

# CORS preflight handler

@app.before_request

def handle_preflight():

    if request.method == "OPTIONS":

        response = jsonify({'status': 'OK'})

        response.headers.add("Access-Control-Allow-Origin", "*")

        response.headers.add('Access-Control-Allow-Headers', "*")

        response.headers.add('Access-Control-Allow-Methods', "*")

        return response

  

# LiveKit Configuration

LIVEKIT_URL = os.getenv('LIVEKIT_URL')

LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')

LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')

  

# AWS Configuration for Recording

AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID', '********************')

AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY', 'lGbBX7ci33hdOEyETiq2f2hlInAbBnB0w/YKl1F5')

AWS_REGION = os.getenv('AWS_REGION', 'eu-north-1')

S3_BUCKET_NAME = os.getenv('S3_BUCKET_NAME', 'class-recordings-demo-kota-dsais.com')

CLOUDFRONT_DOMAIN = os.getenv('CLOUDFRONT_DOMAIN', 'dbttglsg21kbp.cloudfront.net')

  

# Lazy initialization for LiveKit API client

livekit_api = None

def get_livekit_api():

    global livekit_api

    if livekit_api is None:

        livekit_api = LiveKitAPI(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET)

    return livekit_api

  

# Global storage

enhanced_streams = {}

stream_recordings = {}

chat_messages = {}

quality_settings = {}

livekit_rooms = {}

  

# Recording Manager Class

class RecordingManager:

    """Manages LiveKit recordings with S3 storage and CloudFront distribution"""

  

    def __init__(self):

        # Initialize S3 client (this doesn't require event loop)

        self.s3_client = boto3.client(

            's3',

            aws_access_key_id=AWS_ACCESS_KEY_ID,

            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,

            region_name=AWS_REGION

        )

  

        # Active recordings tracking

        self.active_recordings: Dict[str, Dict[str, Any]] = {}

  

        # Check if egress is available

        self.egress_available = True

  

        # Lazy initialization for LiveKit API (will be created when needed)

        self._livekit_api = None

  

    async def get_livekit_api(self):

        """Get or create LiveKit API client with proper async handling"""

        if self._livekit_api is None:

            try:

                # Create a new client session for this event loop

                import aiohttp

                session = aiohttp.ClientSession()

                self._livekit_api = api.LiveKitAPI(

                    LIVEKIT_URL,

                    LIVEKIT_API_KEY,

                    LIVEKIT_API_SECRET,

                    session=session

                )

                logger.info("LiveKit API client initialized successfully")

            except Exception as e:

                logger.error(f"Failed to initialize LiveKit API client: {e}")

                # Return None to indicate failure

                return None

        return self._livekit_api

  

    async def start_room_recording(self, room_name: str, session_id: str,

                                 layout: str = "grid", quality: str = "high") -> Optional[str]:

        """Start recording a LiveKit room using official LiveKit API"""

        try:

            # Generate S3 key for the recording

            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")

            s3_key = f"recordings/{session_id}/{session_id}_{timestamp}.mp4"

  

            # Get quality settings

            quality_settings = self._get_quality_settings(quality)

  

            # Create room composite egress request using official LiveKit API

            # Create the file output

            file_output = api.EncodedFileOutput(

                file_type=api.EncodedFileType.MP4,

                filepath=s3_key,

                s3=api.S3Upload(

                    bucket=S3_BUCKET_NAME,

                    region=AWS_REGION,

                    access_key=AWS_ACCESS_KEY_ID,

                    secret=AWS_SECRET_ACCESS_KEY,

                ),

            )

            # Create advanced encoding options with quality settings

            advanced_options = api.EncodingOptions(

                width=quality_settings.get('width', 1920),

                height=quality_settings.get('height', 1080),

                depth=quality_settings.get('depth', 24),

                framerate=quality_settings.get('framerate', 30),

                audio_frequency=quality_settings.get('audio_frequency', 48000),

                audio_bitrate=quality_settings.get('audio_bitrate', 128000),

                video_bitrate=quality_settings.get('video_bitrate', 4500000)

            )

            # Create the request with file_outputs and advanced options

            req = api.RoomCompositeEgressRequest(

                room_name=room_name,

                layout=layout,

                audio_only=False,

                video_only=False,

                # Use file_outputs instead of output field

                file_outputs=[file_output],

                # Use advanced encoding options

                advanced=advanced_options

            )

  

            # Start recording using LiveKit API

            try:

                livekit_api = await self.get_livekit_api()

                if livekit_api is None:

                    logger.warning("LiveKit API not available, using mock recording")

                    return await self._start_mock_recording(room_name, session_id, layout, quality)

  

                res = await livekit_api.egress.start_room_composite_egress(req)

                egress_id = res.egress_id

  

                # Store recording metadata

                self.active_recordings[session_id] = {

                    'egress_id': egress_id,

                    'room_name': room_name,

                    's3_key': s3_key,

                    'started_at': datetime.now(timezone.utc),

                    'status': 'recording',

                    'layout': layout,

                    'quality': quality,

                    'egress_info': res

                }

  

                logger.info(f"Recording started for room {room_name}, egress_id: {egress_id}")

                return egress_id

  

            except Exception as e:

                logger.warning(f"LiveKit API recording failed: {e}, using mock recording")

                return await self._start_mock_recording(room_name, session_id, layout, quality)

  

        except Exception as e:

            logger.error(f"Failed to start recording for room {room_name}: {e}")

            return await self._start_mock_recording(room_name, session_id, layout, quality)

  

    async def _start_mock_recording(self, room_name: str, session_id: str, layout: str, quality: str) -> str:

        """Start a mock recording for testing purposes"""

        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")

        s3_key = f"recordings/{session_id}/{session_id}_{timestamp}.mp4"

        egress_id = f"mock_{session_id}_{timestamp}"

  

        # Store recording metadata

        self.active_recordings[session_id] = {

            'egress_id': egress_id,

            'room_name': room_name,

            's3_key': s3_key,

            'started_at': datetime.now(timezone.utc),

            'status': 'recording',

            'layout': layout,

            'quality': quality,

            'mock': True

        }

  

        logger.info(f"Mock recording started for room {room_name}, egress_id: {egress_id}")

        return egress_id

  

    async def stop_recording(self, session_id: str) -> Optional[Dict[str, Any]]:

        """Stop recording for a session using official LiveKit API"""

        try:

            if session_id not in self.active_recordings:

                logger.warning(f"No active recording found for session {session_id}")

                return None

  

            recording_info = self.active_recordings[session_id]

            egress_id = recording_info['egress_id']

  

            # Check if it's a mock recording

            if recording_info.get('mock', False):

                # For mock recordings, just update status

                recording_info['status'] = 'completed'

                recording_info['stopped_at'] = datetime.now(timezone.utc)

                logger.info(f"Mock recording stopped for session {session_id}")

                return recording_info

  

            # Stop recording using official LiveKit API

            try:

                livekit_api = await self.get_livekit_api()

                if livekit_api is None:

                    logger.warning("LiveKit API not available for stopping recording")

                    # Still mark as completed even if API call failed

                    recording_info['status'] = 'completed'

                    recording_info['stopped_at'] = datetime.now(timezone.utc)

                    return recording_info

  

                res = await livekit_api.egress.stop_egress(egress_id)

  

                # Update recording status

                recording_info['status'] = 'completed'

                recording_info['stopped_at'] = datetime.now(timezone.utc)

                recording_info['final_egress_info'] = res

  

                logger.info(f"Recording stopped for session {session_id}, egress_id: {egress_id}")

                return recording_info

  

            except Exception as e:

                logger.warning(f"LiveKit API stop failed: {e}")

                # Still mark as completed even if API call failed

                recording_info['status'] = 'completed'

                recording_info['stopped_at'] = datetime.now(timezone.utc)

                return recording_info

  

        except Exception as e:

            logger.error(f"Failed to stop recording for session {session_id}: {e}")

            return None

  

    async def get_recording_status(self, session_id: str) -> Optional[Dict[str, Any]]:

        """Get current recording status"""

        try:

            if session_id not in self.active_recordings:

                return None

  

            recording_info = self.active_recordings[session_id]

            egress_id = recording_info['egress_id']

  

            # For mock recordings, return stored info

            if recording_info.get('mock', False):

                return recording_info

  

            # Get status from LiveKit API

            try:

                livekit_api = await self.get_livekit_api()

                if livekit_api is None:

                    logger.warning("LiveKit API not available for status check")

                    return recording_info

  

                egress_list = await livekit_api.egress.list_egress(room_name="", egress_id=egress_id)

  

                if egress_list and len(egress_list) > 0:

                    egress_info = egress_list[0]

                    recording_info['current_status'] = egress_info.status

                    recording_info['updated_at'] = datetime.now(timezone.utc)

  

                return recording_info

  

            except Exception as e:

                logger.warning(f"Failed to get recording status from API: {e}")

                return recording_info

  

        except Exception as e:

            logger.error(f"Failed to get recording status for session {session_id}: {e}")

            return None

  

    def get_cloudfront_url(self, s3_key: str) -> str:

        """Generate CloudFront URL for recorded video"""

        return f"https://{CLOUDFRONT_DOMAIN}/{s3_key}"

  

    def get_s3_presigned_url(self, s3_key: str, expiration: int = 3600) -> Optional[str]:

        """Generate presigned S3 URL for temporary access"""

        try:

            url = self.s3_client.generate_presigned_url(

                'get_object',

                Params={'Bucket': S3_BUCKET_NAME, 'Key': s3_key},

                ExpiresIn=expiration

            )

            return url

        except ClientError as e:

            logger.error(f"Failed to generate presigned URL: {e}")

            return None

  

    def get_s3_file_size(self, s3_key: str) -> int:

        """Get file size from S3"""

        try:

            response = self.s3_client.head_object(Bucket=S3_BUCKET_NAME, Key=s3_key)

            return response['ContentLength']

        except ClientError as e:

            logger.warning(f"Could not get file size for {s3_key}: {e}")

            return 0

  

    def _get_quality_settings(self, quality: str) -> Dict[str, Any]:

        """Get recording quality parameters"""

        quality_presets = {

            "high": {

                "width": 1920,

                "height": 1080,

                "depth": 24,

                "framerate": 30,

                "audio_frequency": 48000,

                "audio_bitrate": 128000,

                "video_bitrate": 4500000

            },

            "medium": {

                "width": 1280,

                "height": 720,

                "depth": 24,

                "framerate": 30,

                "audio_frequency": 44100,

                "audio_bitrate": 96000,

                "video_bitrate": 2500000

            },

            "low": {

                "width": 854,

                "height": 480,

                "depth": 24,

                "framerate": 24,

                "audio_frequency": 44100,

                "audio_bitrate": 64000,

                "video_bitrate": 1000000

            }

        }

  

        return quality_presets.get(quality, quality_presets["medium"])

  

    async def cleanup_completed_recordings(self):

        """Clean up completed recordings from memory"""

        completed_sessions = []

  

        for session_id, recording_info in self.active_recordings.items():

            if recording_info.get('status') in ['completed', 'failed', 'stopped']:

                # Keep for 1 hour after completion for status queries

                if recording_info.get('stopped_at'):

                    time_since_stop = datetime.now(timezone.utc) - recording_info['stopped_at']

                    if time_since_stop.total_seconds() > 3600:  # 1 hour

                        completed_sessions.append(session_id)

  

        for session_id in completed_sessions:

            del self.active_recordings[session_id]

            logger.info(f"Cleaned up recording metadata for session {session_id}")

  

class EnhancedStreamManager:

    def __init__(self):

        self.streams = {}

  

    def create_enhanced_stream(self, teacher_id, session_id, teacher_sid, quality):

        self.streams[session_id] = {

            'teacher_id': teacher_id,

            'session_id': session_id,

            'teacher_sid': teacher_sid,

            'quality': quality,

            'viewer_sids': [],

            'start_time': datetime.now(timezone.utc),

            'status': 'active',

            'viewer_count': 0,

            'chat_enabled': True,

            'recording_enabled': True,

            'screen_sharing': True

        }

        return self.streams[session_id]

  

    def get_stream(self, session_id):

        return self.streams.get(session_id)

  

    def add_viewer(self, session_id, viewer_sid):

        stream = self.get_stream(session_id)

        if stream:

            if viewer_sid not in stream['viewer_sids']:

                stream['viewer_sids'].append(viewer_sid)

                stream['viewer_count'] += 1

            return True

        return False

  

    def remove_viewer(self, session_id, viewer_sid):

        stream = self.get_stream(session_id)

        if stream and viewer_sid in stream['viewer_sids']:

            stream['viewer_sids'].remove(viewer_sid)

            stream['viewer_count'] = max(0, stream['viewer_count'] - 1)

            return True

        return False

  

    def update_stream_status(self, session_id, status):

        stream = self.get_stream(session_id)

        if stream:

            stream['status'] = status

            return True

        return False

  

class LiveKitManager:

    def __init__(self):

        self.rooms = {}

  

    def generate_access_token(self, room_name, participant_identity, participant_name=None, is_teacher=False):

        try:

            token = AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)

            token.with_identity(participant_identity)

            token.with_ttl(timedelta(seconds=18000))  # 5 hours

            video_grants = VideoGrants()

            video_grants.room = room_name

            video_grants.room_join = True

            if is_teacher:

                video_grants.can_publish = True

                video_grants.can_subscribe = True

                video_grants.can_publish_data = True

            else:

                video_grants.can_publish = False

                video_grants.can_subscribe = True

                video_grants.can_publish_data = False

            token.with_grants(video_grants)

            if participant_name:

                token.with_name(participant_name)

            return token.to_jwt()

        except Exception as e:

            logger.error(f"Error generating LiveKit token: {e}")

            return None

  

    async def create_room(self, room_name, max_participants=50):

        try:

            room_request = api.CreateRoomRequest(

                name=room_name,

                max_participants=max_participants,

                empty_timeout=30 * 60,  # 30 minutes

                departure_timeout=120   # 2 minutes

            )

            # Use the recording_manager's async get_livekit_api method

            api_client = await recording_manager.get_livekit_api()

            room = await api_client.room.create_room(room_request)

            self.rooms[room_name] = {

                'room': room,

                'created_at': datetime.now(timezone.utc),

                'participants': [],

                'max_participants': max_participants

            }

            logger.info(f"Created LiveKit room: {room_name}")

            return room

        except Exception as e:

            logger.error(f"Error creating LiveKit room {room_name}: {e}")

            return None

  

    async def delete_room(self, room_name):

        try:

            # Use the recording_manager's async get_livekit_api method

            api_client = await recording_manager.get_livekit_api()

            # Create a DeleteRoomRequest object instead of passing string directly

            delete_request = api.DeleteRoomRequest(room=room_name)

            await api_client.room.delete_room(delete_request)

            if room_name in self.rooms:

                del self.rooms[room_name]

            logger.info(f"Deleted LiveKit room: {room_name}")

            return True

        except Exception as e:

            logger.error(f"Error deleting LiveKit room {room_name}: {e}")

            return False

  

    async def list_rooms(self):

        try:

            # Use the recording_manager's async get_livekit_api method

            api_client = await recording_manager.get_livekit_api()

            rooms = await api_client.room.list_rooms()

            return rooms

        except Exception as e:

            logger.error(f"Error listing LiveKit rooms: {e}")

            return []

  

# Initialize managers

enhanced_stream_manager = EnhancedStreamManager()

livekit_manager = LiveKitManager()

recording_manager = RecordingManager()

  

# Original health check endpoint removed - using enhanced version below

  

# HTTP-based Chat API Endpoints

@app.route('/api/chat/send', methods=['POST'])

def send_chat_message():

    try:

        data = request.get_json()

        session_id = data.get('session_id')

        message = data.get('message')

        sender_id = data.get('sender_id')

        sender_name = data.get('sender_name', 'Anonymous')

        if not session_id or not message or not sender_id:

            return jsonify({'message': 'session_id, message, and sender_id are required'}), 400

        db.execute_query(

            "INSERT INTO livekit_chat_messages (session_id, message, sender_id, sender_name, timestamp) "

            "VALUES (%s, %s, %s, %s, NOW())",

            (session_id, message, sender_id, sender_name)

        )

        socketio.emit('chat_message', {

            'session_id': session_id,

            'message': message,

            'sender_id': sender_id,

            'sender_name': sender_name,

            'timestamp': datetime.now().isoformat()

        }, room=session_id)

        return jsonify({'message': 'Chat message sent successfully'}), 200

    except Exception as e:

        logger.error(f"Chat message error: {e}")

        return jsonify({'message': 'Failed to send chat message', 'error': str(e)}), 500

  

@app.route('/api/chat/history', methods=['GET'])

def get_chat_history():

    try:

        session_id = request.args.get('session_id')

        if not session_id:

            return jsonify({'message': 'session_id is required'}), 400

        messages = db.execute_query(

            "SELECT * FROM livekit_chat_messages WHERE session_id = %s ORDER BY timestamp ASC",

            (session_id,)

        )

        return jsonify({

            'session_id': session_id,

            'messages': [{

                'id': msg['id'],

                'message': msg['message'],

                'sender_id': msg['sender_id'],

                'sender_name': msg['sender_name'],

                'timestamp': msg['timestamp'].isoformat()

            } for msg in messages]

        }), 200

    except Exception as e:

        logger.error(f"Chat history error: {e}")

        return jsonify({'message': 'Failed to get chat history', 'error': str(e)}), 500

  

@app.route('/api/chat/history/<session_id>', methods=['GET'])

def get_chat_history_by_path(session_id):

    """Alternative endpoint that accepts session_id as path parameter"""

    try:

        if not session_id:

            return jsonify({'message': 'session_id is required'}), 400

  

        # Use the same logic as the query parameter version

        messages = db.execute_query(

            "SELECT * FROM livekit_chat_messages WHERE session_id = %s ORDER BY timestamp ASC",

            (session_id,)

        )

  

        if messages is None:

            messages = []

  

        return jsonify({

            'session_id': session_id,

            'messages': [{

                'id': msg['id'],

                'message': msg['message'],

                'sender_id': msg['sender_id'],

                'sender_name': msg['sender_name'],

                'timestamp': msg['timestamp'].isoformat()

            } for msg in messages]

        }), 200

    except Exception as e:

        logger.error(f"Chat history error (path): {e}")

        return jsonify({'message': 'Failed to get chat history', 'error': str(e)}), 500

  

@app.route('/api/enhanced-stream/start', methods=['POST'])

def start_enhanced_stream():

    try:

        data = request.get_json()

        session_id = data.get('session_id', str(uuid.uuid4()))

        quality = data.get('quality', 'medium')

        teacher_id = data.get('teacher_id', 'demo_teacher')

        teacher_name = data.get('teacher_name', teacher_id)

        recording_enabled = data.get('recording_enabled', True)

        layout = data.get('layout', 'grid')

  

        # Create enhanced stream

        stream = enhanced_stream_manager.create_enhanced_stream(teacher_id, session_id, None, quality)

  

        # Generate LiveKit token

        teacher_token = livekit_manager.generate_access_token(

            room_name=session_id,

            participant_identity=teacher_id,

            participant_name=teacher_name,

            is_teacher=True

        )

        if not teacher_token:

            return jsonify({'message': 'Failed to generate LiveKit token'}), 500

  

        # Create a single event loop for all async operations

        loop = asyncio.new_event_loop()

        asyncio.set_event_loop(loop)

        # Create LiveKit room

        if session_id not in livekit_manager.rooms:

            loop.run_until_complete(livekit_manager.create_room(session_id))

  

        # Start recording if enabled

        egress_id = None

        if recording_enabled:

            try:

                egress_id = loop.run_until_complete(

                    recording_manager.start_room_recording(

                        room_name=session_id,

                        session_id=session_id,

                        layout=layout,

                        quality=quality

                    )

                )

  

                # Update stream with recording info

                stream['recording_enabled'] = True

                stream['egress_id'] = egress_id

                stream['recording_status'] = 'recording' if egress_id else 'failed'

  

                logger.info(f"Recording started for session {session_id}, egress_id: {egress_id}")

  

            except Exception as e:

                logger.error(f"Failed to start recording: {e}")

                stream['recording_enabled'] = False

                stream['recording_status'] = 'failed'

  

        return jsonify({

            'session_id': session_id,

            'livekit_token': teacher_token,

            'livekit_url': LIVEKIT_URL,

            'roomName': session_id,

            'quality': quality,

            'recording_enabled': recording_enabled,

            'egress_id': egress_id,

            'layout': layout,

            'start_time': stream['start_time'].isoformat(),

            'message': 'Enhanced stream started successfully'

        }), 200

    except Exception as e:

        logger.error(f"Enhanced stream start error: {e}")

        return jsonify({'message': 'Failed to start enhanced stream'}), 500

  

@app.route('/api/enhanced-stream/refresh-token', methods=['POST'])

def refresh_livekit_token():

    try:

        data = request.get_json()

        session_id = data.get('session_id')

        room_name = data.get('room_name', session_id)

        if not session_id:

            return jsonify({'message': 'session_id is required'}), 400

        stream = enhanced_stream_manager.get_stream(session_id)

        if not stream:

            return jsonify({'message': 'Stream not found'}), 404

        user_id = data.get('user_id', f"refresh-{session_id}")

        user_name = data.get('user_name', user_id)

        is_teacher = data.get('is_teacher', False)

        token = livekit_manager.generate_access_token(

            room_name=room_name,

            participant_identity=user_id,

            participant_name=user_name,

            is_teacher=is_teacher

        )

        if token:

            return jsonify({

                'livekit_token': token,

                'room_name': room_name,

                'expires_in': 18000

            }), 200

        else:

            return jsonify({'message': 'Failed to generate token'}), 500

    except Exception as e:

        logger.error(f"Token refresh error: {e}")

        return jsonify({'message': 'Failed to refresh token', 'error': str(e)}), 500

  

@app.route('/api/livekit/token', methods=['POST'])

def generate_livekit_token():

    try:

        data = request.get_json()

        room_name = data.get('room_name') or data.get('session_id')

        participant_id = data.get('participant_id') or data.get('user_id')

        participant_name = data.get('participant_name', participant_id)

        is_teacher = data.get('is_teacher', False)

        if not room_name or not participant_id:

            return jsonify({'message': 'room_name and participant_id are required'}), 400

        stream = enhanced_stream_manager.get_stream(room_name)

        if not stream:

            return jsonify({'message': 'Stream not found'}), 404

        token = livekit_manager.generate_access_token(

            room_name=room_name,

            participant_identity=participant_id,

            participant_name=participant_name,

            is_teacher=is_teacher

        )

        if token:

            return jsonify({

                'token': token,

                'livekit_url': LIVEKIT_URL,

                'room_name': room_name,

                'participant_id': participant_id,

                'is_teacher': is_teacher,

                'token_expires_in': 18000

            }), 200

        else:

            return jsonify({'message': 'Failed to generate token'}), 500

    except Exception as e:

        logger.error(f"Token generation error: {e}")

        return jsonify({'message': 'Failed to generate token'}), 500

  

@app.route('/api/livekit/join', methods=['POST'])

def join_livekit_room():

    try:

        data = request.get_json()

        session_id = data.get('session_id')

        user_id = data.get('user_id')

        user_name = data.get('user_name', user_id)

        user_role = data.get('user_role', 'student')

        if not session_id or not user_id:

            return jsonify({'message': 'session_id and user_id are required'}), 400

        stream = enhanced_stream_manager.get_stream(session_id)

        if not stream:

            return jsonify({'message': 'Stream not found'}), 404

        if user_role != 'teacher':

            enhanced_stream_manager.add_viewer(session_id, f"viewer-{user_id}")

        is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))

        token = livekit_manager.generate_access_token(

            room_name=session_id,

            participant_identity=user_id,

            participant_name=user_name,

            is_teacher=is_teacher

        )

        if token:

            return jsonify({

                'session_id': session_id,

                'livekit_url': LIVEKIT_URL,

                'livekit_token': token,

                'participant_id': user_id,

                'participant_name': user_name,

                'is_teacher': is_teacher,

                'token_expires_in': 18000

            }), 200

        else:

            return jsonify({'message': 'Failed to generate LiveKit token'}), 500

    except Exception as e:

        logger.error(f"Error joining LiveKit room: {e}")

        return jsonify({'message': 'Failed to join LiveKit room', 'error': str(e)}), 500

  

@app.route('/api/enhanced-stream/stop', methods=['POST'])

def stop_enhanced_stream():

    try:

        data = request.get_json()

        session_id = data.get('session_id')

  

        if not session_id:

            return jsonify({'message': 'session_id is required'}), 400

  

        # Get stream info

        stream = enhanced_stream_manager.get_stream(session_id)

        recording_info = None

  

        # Create a single event loop for all async operations

        loop = asyncio.new_event_loop()

        asyncio.set_event_loop(loop)

        # Stop recording if exists

        if stream and stream.get('egress_id'):

            try:

                recording_info = loop.run_until_complete(

                    recording_manager.stop_recording(session_id)

                )

                logger.info(f"Recording stopped for session {session_id}")

  

                # Store recording info in database

                if recording_info:

                    store_recording_in_database(session_id, recording_info)

  

            except Exception as e:

                logger.error(f"Failed to stop recording: {e}")

  

        # Remove stream from manager

        if session_id in enhanced_stream_manager.streams:

            del enhanced_stream_manager.streams[session_id]

  

        # Delete LiveKit room

        loop.run_until_complete(livekit_manager.delete_room(session_id))

  

        response_data = {

            'message': 'Stream stopped successfully',

            'session_id': session_id

        }

  

        if recording_info:

            response_data['recording'] = {

                'status': recording_info.get('status'),

                's3_key': recording_info.get('s3_key'),

                'cloudfront_url': recording_manager.get_cloudfront_url(recording_info.get('s3_key', ''))

            }

  

        return jsonify(response_data), 200

  

    except Exception as e:

        logger.error(f"Stream stop error: {e}")

        return jsonify({'message': 'Failed to stop stream', 'error': str(e)}), 500

  

@app.route('/active-streams', methods=['GET'])

def get_active_streams():

    try:

        active_streams = []

        for session_id, stream in enhanced_stream_manager.streams.items():

            if stream['status'] == 'active':

                stream_info = {

                    'session_id': session_id,

                    'teacher_id': stream['teacher_id'],

                    'viewer_count': stream['viewer_count'],

                    'quality': stream['quality'],

                    'created_at': stream['start_time'].isoformat(),

                    'uptime': (datetime.now(timezone.utc) - stream['start_time']).total_seconds(),

                    'features': {

                        'chat_enabled': stream.get('chat_enabled', True),

                        'recording_enabled': stream.get('recording_enabled', True),

                        'screen_sharing': stream.get('screen_sharing', True)

                    }

                }

                active_streams.append(stream_info)

        return jsonify({

            'success': True,

            'streams': active_streams,

            'active_streams': active_streams,

            'total_count': len(active_streams),

            'service': 'Enhanced Real-time Streaming',

            'timestamp': datetime.now(timezone.utc).isoformat()

        }), 200

    except Exception as e:

        logger.error(f"Error getting active streams: {e}")

        return jsonify({'message': 'Failed to get active streams', 'error': str(e)}), 500

  

@app.route('/api/enhanced-stream/list', methods=['GET'])

def list_enhanced_streams():

    try:

        streams = []

        for session_id, stream in enhanced_stream_manager.streams.items():

            streams.append({

                'session_id': session_id,

                'teacher_id': stream['teacher_id'],

                'quality': stream['quality'],

                'status': stream['status'],

                'viewer_count': stream['viewer_count'],

                'start_time': stream['start_time'].isoformat(),

                'room_exists': session_id in livekit_manager.rooms

            })

        return jsonify({

            'streams': streams,

            'total': len(streams),

            'timestamp': datetime.now(timezone.utc).isoformat()

        }), 200

    except Exception as e:

        logger.error(f"Error listing streams: {e}")

        return jsonify({'message': 'Failed to list streams', 'error': str(e)}), 500

  

# Helper function to store recording in database

def store_recording_in_database(session_id: str, recording_info: dict):

    """Store recording metadata in PostgreSQL database"""

    try:

        # Calculate duration in seconds

        duration = 0

        if recording_info.get('started_at') and recording_info.get('stopped_at'):

            start_time = recording_info['started_at']

            stop_time = recording_info['stopped_at']

            if isinstance(start_time, str):

                start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))

            if isinstance(stop_time, str):

                stop_time = datetime.fromisoformat(stop_time.replace('Z', '+00:00'))

            duration = (stop_time - start_time).total_seconds()

  

        # Get file size from S3 if possible

        file_size = 0

        s3_key = recording_info.get('s3_key', '')

        if s3_key and not recording_info.get('mock', False):

            file_size = recording_manager.get_s3_file_size(s3_key)

        else:

            # Mock file size for testing

            file_size = 1024 * 1024 * 50  # 50MB mock size

  

        # Insert into your existing stream_recordings table

        try:

            db.execute_query(

                """INSERT INTO stream_recordings

                   (session_id, recording_path, start_time, end_time, duration, file_size)

                   VALUES (%s, %s, %s, %s, %s, %s)""",

                (

                    session_id,

                    recording_info.get('s3_key', ''),

                    recording_info.get('started_at'),

                    recording_info.get('stopped_at'),

                    duration,

                    file_size

                )

            )

            logger.info(f"Recording metadata stored in stream_recordings table for session {session_id}")

  

        except Exception as e:

            logger.error(f"Failed to insert into stream_recordings table: {e}")

            # Try to create table if it doesn't exist

            try:

                db.execute_query(

                    """CREATE TABLE IF NOT EXISTS stream_recordings (

                        id SERIAL PRIMARY KEY,

                        session_id VARCHAR(255) NOT NULL,

                        recording_path TEXT NOT NULL,

                        start_time TIMESTAMP NOT NULL,

                        end_time TIMESTAMP NOT NULL,

                        duration NUMERIC NOT NULL,

                        file_size BIGINT NOT NULL,

                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP

                    )"""

                )

                logger.info("Created stream_recordings table")

  

                # Try insert again

                db.execute_query(

                    """INSERT INTO stream_recordings

                       (session_id, recording_path, start_time, end_time, duration, file_size)

                       VALUES (%s, %s, %s, %s, %s, %s)""",

                    (

                        session_id,

                        recording_info.get('s3_key', ''),

                        recording_info.get('started_at'),

                        recording_info.get('stopped_at'),

                        duration,

                        file_size

                    )

                )

                logger.info(f"Recording metadata stored after creating table for session {session_id}")

  

            except Exception as create_error:

                logger.error(f"Failed to create table and insert: {create_error}")

  

        # Also update streaming_sessions table if it exists

        try:

            db.execute_query(

                """UPDATE streaming_sessions

                   SET recording_url = %s, ended_at = %s

                   WHERE id = %s""",

                (recording_info.get('s3_key'), recording_info.get('stopped_at'), session_id)

            )

            logger.info(f"Updated streaming_sessions table for session {session_id}")

        except Exception as e:

            logger.warning(f"Could not update streaming_sessions table: {e}")

  

    except Exception as e:

        logger.error(f"Failed to store recording metadata: {e}")

  

# New endpoint: Get recorded videos

@app.route('/api/recordings', methods=['GET'])

def get_recorded_videos():

    """Get all recorded videos with pagination and filtering"""

    try:

        # Get pagination parameters

        page = int(request.args.get('page', 1))

        limit = int(request.args.get('limit', 10))

        teacher_id = request.args.get('teacher_id')

  

        # Build query for your PostgreSQL table structure

        base_query = """

            SELECT sr.id, sr.session_id, sr.recording_path, sr.start_time,

                   sr.end_time, sr.duration, sr.file_size, sr.created_at,

                   ss.teacher_id, ss.title

            FROM stream_recordings sr

            LEFT JOIN streaming_sessions ss ON sr.session_id = ss.id

            WHERE sr.recording_path IS NOT NULL AND sr.recording_path != ''

        """

        params = []

  

        if teacher_id:

            base_query += " AND ss.teacher_id = %s"

            params.append(teacher_id)

  

        base_query += " ORDER BY sr.created_at DESC LIMIT %s OFFSET %s"

        params.extend([limit, (page - 1) * limit])

  

        # Execute query

        recordings = db.execute_query(base_query, params)

  

        if recordings is None:

            recordings = []

  

        # Add CloudFront URLs and format response

        formatted_recordings = []

        for recording in recordings:

            formatted_recording = {

                'id': recording.get('id'),

                'session_id': recording.get('session_id'),

                'recording_path': recording.get('recording_path'),

                's3_key': recording.get('recording_path'),  # Use recording_path as s3_key

                'start_time': recording.get('start_time'),

                'end_time': recording.get('end_time'),

                'duration': float(recording.get('duration', 0)),

                'file_size': recording.get('file_size'),

                'created_at': recording.get('created_at'),

                'teacher_id': recording.get('teacher_id'),

                'title': recording.get('title'),

                'status': 'completed'

            }

  

            # Add CloudFront URLs if recording path exists

            if recording.get('recording_path'):

                formatted_recording['cloudfront_url'] = recording_manager.get_cloudfront_url(recording['recording_path'])

                formatted_recording['download_url'] = recording_manager.get_s3_presigned_url(recording['recording_path'])

  

            formatted_recordings.append(formatted_recording)

  

        return jsonify({

            'recordings': formatted_recordings,

            'page': page,

            'limit': limit,

            'total': len(formatted_recordings)

        }), 200

  

    except Exception as e:

        logger.error(f"Error getting recordings: {e}")

        return jsonify({'message': 'Failed to get recordings', 'error': str(e)}), 500

  

# New endpoint: Get specific recording by session ID

@app.route('/api/recordings/<session_id>', methods=['GET'])

def get_recording_by_session(session_id):

    """Get specific recording by session ID"""

    try:

        # Get recording from database using your table structure

        recording = db.execute_query(

            """SELECT sr.id, sr.session_id, sr.recording_path, sr.start_time,

                      sr.end_time, sr.duration, sr.file_size, sr.created_at,

                      ss.teacher_id, ss.title

               FROM stream_recordings sr

               LEFT JOIN streaming_sessions ss ON sr.session_id = ss.id

               WHERE sr.session_id = %s AND sr.recording_path IS NOT NULL

               ORDER BY sr.created_at DESC LIMIT 1""",

            (session_id,)

        )

  

        if not recording or len(recording) == 0:

            return jsonify({'message': 'Recording not found'}), 404

  

        recording = recording[0]

  

        # Format response

        formatted_recording = {

            'id': recording.get('id'),

            'session_id': recording.get('session_id'),

            'recording_path': recording.get('recording_path'),

            's3_key': recording.get('recording_path'),

            'start_time': recording.get('start_time'),

            'end_time': recording.get('end_time'),

            'duration': float(recording.get('duration', 0)),

            'file_size': recording.get('file_size'),

            'created_at': recording.get('created_at'),

            'teacher_id': recording.get('teacher_id'),

            'title': recording.get('title'),

            'status': 'completed'

        }

  

        # Add CloudFront URL

        if recording.get('recording_path'):

            formatted_recording['cloudfront_url'] = recording_manager.get_cloudfront_url(recording['recording_path'])

            formatted_recording['download_url'] = recording_manager.get_s3_presigned_url(recording['recording_path'])

  

        return jsonify(formatted_recording), 200

  

    except Exception as e:

        logger.error(f"Error getting recording for session {session_id}: {e}")

        return jsonify({'message': 'Failed to get recording', 'error': str(e)}), 500

  

# New endpoint: Get recording status

@app.route('/api/recordings/<session_id>/status', methods=['GET'])

def get_recording_status(session_id):

    """Get current recording status"""

    try:

        # Check if recording is active

        if session_id in recording_manager.active_recordings:

            # Create a single event loop for all async operations

            loop = asyncio.new_event_loop()

            asyncio.set_event_loop(loop)

            status = loop.run_until_complete(

                recording_manager.get_recording_status(session_id)

            )

  

            if status:

                return jsonify(status), 200

  

        # Check database for completed recordings using your table structure

        recording = db.execute_query(

            """SELECT sr.id, sr.session_id, sr.recording_path, sr.start_time,

                      sr.end_time, sr.duration, sr.file_size, sr.created_at

               FROM stream_recordings sr

               WHERE sr.session_id = %s

               ORDER BY sr.created_at DESC LIMIT 1""",

            (session_id,)

        )

  

        if recording and len(recording) > 0:

            recording_data = recording[0]

            formatted_status = {

                'id': recording_data.get('id'),

                'session_id': recording_data.get('session_id'),

                'recording_path': recording_data.get('recording_path'),

                's3_key': recording_data.get('recording_path'),

                'start_time': recording_data.get('start_time'),

                'end_time': recording_data.get('end_time'),

                'duration': float(recording_data.get('duration', 0)),

                'file_size': recording_data.get('file_size'),

                'created_at': recording_data.get('created_at'),

                'status': 'completed'

            }

  

            if recording_data.get('recording_path'):

                formatted_status['cloudfront_url'] = recording_manager.get_cloudfront_url(recording_data['recording_path'])

  

            return jsonify(formatted_status), 200

  

        return jsonify({'message': 'Recording not found'}), 404

  

    except Exception as e:

        logger.error(f"Error getting recording status: {e}")

        return jsonify({'message': 'Failed to get recording status', 'error': str(e)}), 500

  

# New endpoint: Download recording

@app.route('/api/recordings/<session_id>/download', methods=['GET'])

def download_recording(session_id):

    """Generate download URL for recording"""

    try:

        # Get recording from database using your table structure

        recording = db.execute_query(

            """SELECT recording_path FROM stream_recordings

               WHERE session_id = %s AND recording_path IS NOT NULL

               ORDER BY created_at DESC LIMIT 1""",

            (session_id,)

        )

  

        if not recording or len(recording) == 0:

            return jsonify({'message': 'Recording not found'}), 404

  

        recording_path = recording[0]['recording_path']

  

        # Generate presigned URL (valid for 1 hour)

        download_url = recording_manager.get_s3_presigned_url(recording_path, expiration=3600)

  

        if not download_url:

            return jsonify({'message': 'Failed to generate download URL'}), 500

  

        return jsonify({

            'download_url': download_url,

            'cloudfront_url': recording_manager.get_cloudfront_url(recording_path),

            'expires_in': 3600,

            'expires_at': (datetime.now(timezone.utc) + timedelta(seconds=3600)).isoformat()

        }), 200

  

    except Exception as e:

        logger.error(f"Error generating download URL: {e}")

        return jsonify({'message': 'Failed to generate download URL', 'error': str(e)}), 500

  

# Enhanced health check with recording status

@app.route('/health', methods=['GET'])

def health_check():

    livekit_status = 'configured' if all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]) else 'not configured'

    aws_status = 'configured' if all([AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION, S3_BUCKET_NAME]) else 'not configured'

  

    return jsonify({

        'status': 'healthy',

        'service': 'Enhanced Real-time Streaming Service with LiveKit Recording',

        'port': 8012,

        'features': ['LiveKit', 'WebRTC', 'Socket.IO', 'Chat', 'Recording', 'Quality Controls', 'S3 Storage', 'CloudFront CDN'],

        'active_streams': len(enhanced_stream_manager.streams),

        'active_recordings': len(recording_manager.active_recordings),

        'livekit': {

            'status': livekit_status,

            'url': LIVEKIT_URL,

            'api_key': LIVEKIT_API_KEY[:8] + '...' if LIVEKIT_API_KEY else None,

            'rooms_managed': len(livekit_manager.rooms),

            'token_ttl': '5 hours (18000 seconds)'

        },

        'aws': {

            'status': aws_status,

            'region': AWS_REGION,

            's3_bucket': S3_BUCKET_NAME,

            'cloudfront_domain': CLOUDFRONT_DOMAIN

        },

        'timestamp': datetime.now().isoformat()

    }), 200

  

# Socket.IO Events

@socketio.on('connect')

def handle_connect():

    logger.info(f"Client connected: {request.sid}")

    emit('connected', {'message': 'Connected to enhanced streaming service'})

  

@socketio.on('disconnect')

def handle_disconnect():

    sid = request.sid

    logger.info(f"Client disconnected: {sid}")

    disconnect_reason = request.environ.get('socketio.disconnect_reason', 'unknown')

    logger.info(f"Disconnect reason for {sid}: {disconnect_reason}")

    if disconnect_reason != 'client disconnected':

        logger.warning(f"Unexpected disconnect for {sid}. Reason: {disconnect_reason}. Check for timeouts or server issues.")

    for session_id in list(enhanced_stream_manager.streams.keys()):

        if enhanced_stream_manager.remove_viewer(session_id, sid):

            stream = enhanced_stream_manager.get_stream(session_id)

            emit('viewer_left', {'viewer_count': stream['viewer_count']}, room=session_id)

  

@socketio.on('join_enhanced_stream')

def handle_join_enhanced_stream(data):

    try:

        session_id = data.get('session_id')

        if not session_id:

            emit('error', {'message': 'Session ID required'})

            return

        join_room(session_id)

        enhanced_stream_manager.add_viewer(session_id, request.sid)

        stream = enhanced_stream_manager.get_stream(session_id)

        if stream:

            emit('viewer_count', {'viewer_count': stream['viewer_count']})

            emit('stream_joined', {

                'session_id': session_id,

                'viewer_count': stream['viewer_count'],

                'status': stream['status']

            })

            emit('viewer_joined', {

                'viewer_count': stream['viewer_count'],

                'viewer_id': request.sid

            }, room=session_id, include_self=False)

        else:

            emit('error', {'message': 'Stream not found'})

    except Exception as e:

        logger.error(f"Error joining enhanced stream: {e}")

        emit('error', {'message': 'Failed to join stream'})

  

@socketio.on('leave_enhanced_stream')

def handle_leave_enhanced_stream(data):

    try:

        session_id = data.get('session_id')

        if session_id:

            leave_room(session_id)

            enhanced_stream_manager.remove_viewer(session_id, request.sid)

            stream = enhanced_stream_manager.get_stream(session_id)

            if stream:

                emit('viewer_left', {'viewer_count': stream['viewer_count']}, room=session_id)

    except Exception as e:

        logger.error(f"Error leaving enhanced stream: {e}")

  

@socketio.on('request_livekit_token')

def handle_request_livekit_token(data):

    try:

        session_id = data.get('session_id')

        user_id = data.get('user_id')

        user_name = data.get('user_name', user_id)

        user_role = data.get('user_role', 'student')

        if not session_id or not user_id:

            emit('error', {'message': 'Session ID and User ID required'})

            return

        stream = enhanced_stream_manager.get_stream(session_id)

        if not stream:

            emit('error', {'message': 'Stream not found'})

            return

        is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))

        token = livekit_manager.generate_access_token(

            room_name=session_id,

            participant_identity=user_id,

            participant_name=user_name,

            is_teacher=is_teacher

        )

        if token:

            emit('livekit_token_generated', {

                'session_id': session_id,

                'livekit_url': LIVEKIT_URL,

                'livekit_token': token,

                'participant_id': user_id,

                'participant_name': user_name,

                'is_teacher': is_teacher,

                'token_expires_in': 18000

            })

        else:

            emit('error', {'message': 'Failed to generate LiveKit token'})

    except Exception as e:

        logger.error(f"Error generating LiveKit token: {e}")

        emit('error', {'message': 'Failed to generate token'})

  

@socketio.on('start_stream')

def handle_start_stream(data):

    try:

        session_id = data.get('session_id')

        teacher_id = data.get('teacher_id')

        teacher_name = data.get('teacher_name', teacher_id)

        quality = data.get('quality', 'medium')

        if not session_id or not teacher_id:

            emit('error', {'message': 'Session ID and Teacher ID required'})

            return

        stream = enhanced_stream_manager.create_enhanced_stream(teacher_id, session_id, request.sid, quality)

        join_room(session_id)

        teacher_token = livekit_manager.generate_access_token(

            room_name=session_id,

            participant_identity=teacher_id,

            participant_name=teacher_name,

            is_teacher=True

        )

        if teacher_token:

            emit('stream_started', {

                'session_id': session_id,

                'livekit_url': LIVEKIT_URL,

                'livekit_token': teacher_token,

                'quality': quality,

                'token_expires_in': 18000

            })

        else:

            emit('error', {'message': 'Failed to generate LiveKit token'})

    except Exception as e:

        logger.error(f"Error starting stream: {e}")

        emit('error', {'message': 'Failed to start stream'})

  

@socketio.on('stop_stream')

def handle_stop_stream(data):

    try:

        session_id = data.get('session_id')

        if not session_id:

            emit('error', {'message': 'Session ID required'})

            return

        if session_id in enhanced_stream_manager.streams:

            del enhanced_stream_manager.streams[session_id]

        leave_room(session_id)

        # Create a single event loop for all async operations

        loop = asyncio.new_event_loop()

        asyncio.set_event_loop(loop)

        loop.run_until_complete(livekit_manager.delete_room(session_id))

        emit('stream_stopped', {'session_id': session_id})

    except Exception as e:

        logger.error(f"Error stopping stream: {e}")

        emit('error', {'message': 'Failed to stop stream'})

  

@socketio.on('enhanced_video_frame')

def handle_enhanced_video_frame(data):

    try:

        session_id = data.get('session_id')

        frame_data = data.get('frame')

        if session_id and frame_data:

            emit('video_frame', {'frame': frame_data}, room=session_id, include_self=False)

    except Exception as e:

        logger.error(f"Video frame error: {e}")

  

# Start the server

if __name__ == '__main__':

    print("=" * 70)

    print(f"ðŸŽ¥ LiveKit URL: {LIVEKIT_URL}")

    print(f"ðŸ”‘ LiveKit API Key: {LIVEKIT_API_KEY}")

    print("ðŸš€ Server starting on port 8012...")

    print("=" * 70)

    if not all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]):

        print("Œ LiveKit configuration missing! Please check your .env file.")

        print("Required: LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET")

        exit(1)

    print("œ… LiveKit configuration validated")

    print("ðŸŽ¬ Ready to create streaming rooms with LiveKit integration")

    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() in ['true', '1', 't']

    socketio.run(app, host='0.0.0.0', port=8012, debug=DEBUG, allow_unsafe_werkzeug=True)

###above code recording only with egress============================


####====================================================================================================================

# from flask import Flask, request, jsonify
# from flask_socketio import SocketIO, emit, join_room, leave_room
# from flask_cors import CORS
# import sys
# import os
# import json
# import uuid
# from datetime import datetime, timezone, timedelta
# import base64
# import asyncio
# import threading
# import logging  # Added for debugging
# # LiveKit imports
# from livekit import api, rtc
# from livekit.api import AccessToken, VideoGrants, LiveKitAPI
# from livekit.rtc import Room, TrackSource
# # Add parent directory to path to import shared modules
# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# from dotenv import load_dotenv
# from shared.database import Database  # Import Database
# # Import ProxyFix for handling reverse proxy headers
# from werkzeug.middleware.proxy_fix import ProxyFix

# # Configure logging
# logging.basicConfig(level=logging.INFO)
# logger = logging.getLogger('StreamingApp')

# load_dotenv()
# app = Flask(__name__)
# app.config['SECRET_KEY'] = 'enhanced-streaming-secret-key'

# # Middleware to keep connections alive
# @app.before_request
# def before_request():
#     if request.headers.get('Upgrade') == 'websocket':
#         socketio.server.eio.ping_timeout = 900  # 15 minutes
#         socketio.server.eio.ping_interval = 20  # 20 seconds

# # Configure CORS
# CORS(app,
#      origins="*",
#      allow_headers=["Content-Type", "Authorization", "Access-Control-Allow-Credentials"],
#      supports_credentials=True,
#      methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"])

# # Apply ProxyFix middleware
# app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1)

# # Initialize Socket.IO
# socketio = SocketIO(app,
#                     cors_allowed_origins="*",
#                     cors_credentials=True,
#                     logger=True,
#                     engineio_logger=True,
#                     ping_timeout=900,  # 15 minutes
#                     ping_interval=20)  # 20 seconds

# # Initialize Database
# db = Database()

# # CORS preflight handler
# @app.before_request
# def handle_preflight():
#     if request.method == "OPTIONS":
#         response = jsonify({'status': 'OK'})
#         response.headers.add("Access-Control-Allow-Origin", "*")
#         response.headers.add('Access-Control-Allow-Headers', "*")
#         response.headers.add('Access-Control-Allow-Methods', "*")
#         return response

# # LiveKit Configuration
# LIVEKIT_URL = os.getenv('LIVEKIT_URL')
# LIVEKIT_API_KEY = os.getenv('LIVEKIT_API_KEY')
# LIVEKIT_API_SECRET = os.getenv('LIVEKIT_API_SECRET')

# # Lazy initialization for LiveKit API client
# livekit_api = None
# def get_livekit_api():
#     global livekit_api
#     if livekit_api is None:
#         livekit_api = LiveKitAPI(LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
#     return livekit_api

# # Global storage
# enhanced_streams = {}
# stream_recordings = {}
# chat_messages = {}
# quality_settings = {}
# livekit_rooms = {}

# class EnhancedStreamManager:
#     def __init__(self):
#         self.streams = {}

#     def create_enhanced_stream(self, teacher_id, session_id, teacher_sid, quality):
#         self.streams[session_id] = {
#             'teacher_id': teacher_id,
#             'session_id': session_id,
#             'teacher_sid': teacher_sid,
#             'quality': quality,
#             'viewer_sids': [],
#             'start_time': datetime.now(timezone.utc),
#             'status': 'active',
#             'viewer_count': 0,
#             'chat_enabled': True,
#             'recording_enabled': True,
#             'screen_sharing': True
#         }
#         return self.streams[session_id]

#     def get_stream(self, session_id):
#         return self.streams.get(session_id)

#     def add_viewer(self, session_id, viewer_sid):
#         stream = self.get_stream(session_id)
#         if stream:
#             if viewer_sid not in stream['viewer_sids']:
#                 stream['viewer_sids'].append(viewer_sid)
#                 stream['viewer_count'] += 1
#             return True
#         return False

#     def remove_viewer(self, session_id, viewer_sid):
#         stream = self.get_stream(session_id)
#         if stream and viewer_sid in stream['viewer_sids']:
#             stream['viewer_sids'].remove(viewer_sid)
#             stream['viewer_count'] = max(0, stream['viewer_count'] - 1)
#             return True
#         return False

#     def update_stream_status(self, session_id, status):
#         stream = self.get_stream(session_id)
#         if stream:
#             stream['status'] = status
#             return True
#         return False

# class LiveKitManager:
#     def __init__(self):
#         self.rooms = {}

#     def generate_access_token(self, room_name, participant_identity, participant_name=None, is_teacher=False):
#         try:
#             token = AccessToken(LIVEKIT_API_KEY, LIVEKIT_API_SECRET)
#             token.with_identity(participant_identity)
#             token.with_ttl(timedelta(seconds=18000))  # 5 hours
#             video_grants = VideoGrants()
#             video_grants.room = room_name
#             video_grants.room_join = True
#             if is_teacher:
#                 video_grants.can_publish = True
#                 video_grants.can_subscribe = True
#                 video_grants.can_publish_data = True
#             else:
#                 video_grants.can_publish = False
#                 video_grants.can_subscribe = True
#                 video_grants.can_publish_data = False
#             token.with_grants(video_grants)
#             if participant_name:
#                 token.with_name(participant_name)
#             return token.to_jwt()
#         except Exception as e:
#             logger.error(f"Error generating LiveKit token: {e}")
#             return None

#     async def create_room(self, room_name, max_participants=50):
#         try:
#             room_request = api.CreateRoomRequest(
#                 name=room_name,
#                 max_participants=max_participants,
#                 empty_timeout=30 * 60,  # 30 minutes
#                 departure_timeout=120   # 2 minutes
#             )
#             api_client = get_livekit_api()
#             room = await api_client.room.create_room(room_request)
#             self.rooms[room_name] = {
#                 'room': room,
#                 'created_at': datetime.now(timezone.utc),
#                 'participants': [],
#                 'max_participants': max_participants
#             }
#             logger.info(f"Created LiveKit room: {room_name}")
#             return room
#         except Exception as e:
#             logger.error(f"Error creating LiveKit room {room_name}: {e}")
#             return None

#     async def delete_room(self, room_name):
#         try:
#             api_client = get_livekit_api()
#             # Create a DeleteRoomRequest object instead of passing string directly
#             delete_request = api.DeleteRoomRequest(room=room_name)
#             await api_client.room.delete_room(delete_request)
#             if room_name in self.rooms:
#                 del self.rooms[room_name]
#             logger.info(f"Deleted LiveKit room: {room_name}")
#             return True
#         except Exception as e:
#             logger.error(f"Error deleting LiveKit room {room_name}: {e}")
#             return False

#     async def list_rooms(self):
#         try:
#             api_client = get_livekit_api()
#             rooms = await api_client.room.list_rooms()
#             return rooms
#         except Exception as e:
#             logger.error(f"Error listing LiveKit rooms: {e}")
#             return []

# # Initialize managers
# enhanced_stream_manager = EnhancedStreamManager()
# livekit_manager = LiveKitManager()

# # Health check endpoint
# @app.route('/health', methods=['GET'])
# def health_check():
#     livekit_status = 'configured' if all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]) else 'not configured'
#     return jsonify({
#         'status': 'healthy',
#         'service': 'Enhanced Real-time Streaming Service with LiveKit',
#         'port': 8012,
#         'features': ['LiveKit', 'WebRTC', 'Socket.IO', 'Chat', 'Recording', 'Quality Controls'],
#         'active_streams': len(enhanced_stream_manager.streams),
#         'livekit': {
#             'status': livekit_status,
#             'url': LIVEKIT_URL,
#             'api_key': LIVEKIT_API_KEY[:8] + '...' if LIVEKIT_API_KEY else None,
#             'rooms_managed': len(livekit_manager.rooms),
#             'token_ttl': '5 hours (18000 seconds)'
#         },
#         'timestamp': datetime.now().isoformat()
#     }), 200

# # HTTP-based Chat API Endpoints
# @app.route('/api/chat/send', methods=['POST'])
# def send_chat_message():
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         message = data.get('message')
#         sender_id = data.get('sender_id')
#         sender_name = data.get('sender_name', 'Anonymous')
#         if not session_id or not message or not sender_id:
#             return jsonify({'message': 'session_id, message, and sender_id are required'}), 400
#         db.execute_query(
#             "INSERT INTO livekit_chat_messages (session_id, message, sender_id, sender_name, timestamp) "
#             "VALUES (%s, %s, %s, %s, NOW())",
#             (session_id, message, sender_id, sender_name)
#         )
#         socketio.emit('chat_message', {
#             'session_id': session_id,
#             'message': message,
#             'sender_id': sender_id,
#             'sender_name': sender_name,
#             'timestamp': datetime.now().isoformat()
#         }, room=session_id)
#         return jsonify({'message': 'Chat message sent successfully'}), 200
#     except Exception as e:
#         logger.error(f"Chat message error: {e}")
#         return jsonify({'message': 'Failed to send chat message', 'error': str(e)}), 500

# @app.route('/api/chat/history', methods=['GET'])
# def get_chat_history():
#     try:
#         session_id = request.args.get('session_id')
#         if not session_id:
#             return jsonify({'message': 'session_id is required'}), 400
#         messages = db.execute_query(
#             "SELECT * FROM livekit_chat_messages WHERE session_id = %s ORDER BY timestamp ASC",
#             (session_id,)
#         )
#         return jsonify({
#             'session_id': session_id,
#             'messages': [{
#                 'id': msg['id'],
#                 'message': msg['message'],
#                 'sender_id': msg['sender_id'],
#                 'sender_name': msg['sender_name'],
#                 'timestamp': msg['timestamp'].isoformat()
#             } for msg in messages]
#         }), 200
#     except Exception as e:
#         logger.error(f"Chat history error: {e}")
#         return jsonify({'message': 'Failed to get chat history', 'error': str(e)}), 500

# @app.route('/api/chat/history/<session_id>', methods=['GET'])
# def get_chat_history_by_path(session_id):
#     """Alternative endpoint that accepts session_id as path parameter"""
#     try:
#         if not session_id:
#             return jsonify({'message': 'session_id is required'}), 400

#         # Use the same logic as the query parameter version
#         messages = db.execute_query(
#             "SELECT * FROM livekit_chat_messages WHERE session_id = %s ORDER BY timestamp ASC",
#             (session_id,)
#         )

#         if messages is None:
#             messages = []

#         return jsonify({
#             'session_id': session_id,
#             'messages': [{
#                 'id': msg['id'],
#                 'message': msg['message'],
#                 'sender_id': msg['sender_id'],
#                 'sender_name': msg['sender_name'],
#                 'timestamp': msg['timestamp'].isoformat()
#             } for msg in messages]
#         }), 200
#     except Exception as e:
#         logger.error(f"Chat history error (path): {e}")
#         return jsonify({'message': 'Failed to get chat history', 'error': str(e)}), 500

# @app.route('/api/enhanced-stream/start', methods=['POST'])
# def start_enhanced_stream():
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id', str(uuid.uuid4()))
#         quality = data.get('quality', 'medium')
#         teacher_id = data.get('teacher_id', 'demo_teacher')
#         teacher_name = data.get('teacher_name', teacher_id)
#         stream = enhanced_stream_manager.create_enhanced_stream(teacher_id, session_id, None, quality)
#         teacher_token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=teacher_id,
#             participant_name=teacher_name,
#             is_teacher=True
#         )
#         if not teacher_token:
#             return jsonify({'message': 'Failed to generate LiveKit token'}), 500
#         if session_id not in livekit_manager.rooms:
#             loop = asyncio.new_event_loop()
#             asyncio.set_event_loop(loop)
#             loop.run_until_complete(livekit_manager.create_room(session_id))
#         return jsonify({
#             'session_id': session_id,
#             'livekit_token': teacher_token,
#             'livekit_url': LIVEKIT_URL,
#             'roomName': session_id,
#             'quality': quality,
#             'start_time': stream['start_time'].isoformat(),
#             'message': 'Enhanced stream started successfully'
#         }), 200
#     except Exception as e:
#         logger.error(f"Enhanced stream start error: {e}")
#         return jsonify({'message': 'Failed to start enhanced stream'}), 500

# @app.route('/api/enhanced-stream/refresh-token', methods=['POST'])
# def refresh_livekit_token():
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         room_name = data.get('room_name', session_id)
#         if not session_id:
#             return jsonify({'message': 'session_id is required'}), 400
#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             return jsonify({'message': 'Stream not found'}), 404
#         user_id = data.get('user_id', f"refresh-{session_id}")
#         user_name = data.get('user_name', user_id)
#         is_teacher = data.get('is_teacher', False)
#         token = livekit_manager.generate_access_token(
#             room_name=room_name,
#             participant_identity=user_id,
#             participant_name=user_name,
#             is_teacher=is_teacher
#         )
#         if token:
#             return jsonify({
#                 'livekit_token': token,
#                 'room_name': room_name,
#                 'expires_in': 18000
#             }), 200
#         else:
#             return jsonify({'message': 'Failed to generate token'}), 500
#     except Exception as e:
#         logger.error(f"Token refresh error: {e}")
#         return jsonify({'message': 'Failed to refresh token', 'error': str(e)}), 500

# @app.route('/api/livekit/token', methods=['POST'])
# def generate_livekit_token():
#     try:
#         data = request.get_json()
#         room_name = data.get('room_name') or data.get('session_id')
#         participant_id = data.get('participant_id') or data.get('user_id')
#         participant_name = data.get('participant_name', participant_id)
#         is_teacher = data.get('is_teacher', False)
#         if not room_name or not participant_id:
#             return jsonify({'message': 'room_name and participant_id are required'}), 400
#         stream = enhanced_stream_manager.get_stream(room_name)
#         if not stream:
#             return jsonify({'message': 'Stream not found'}), 404
#         token = livekit_manager.generate_access_token(
#             room_name=room_name,
#             participant_identity=participant_id,
#             participant_name=participant_name,
#             is_teacher=is_teacher
#         )
#         if token:
#             return jsonify({
#                 'token': token,
#                 'livekit_url': LIVEKIT_URL,
#                 'room_name': room_name,
#                 'participant_id': participant_id,
#                 'is_teacher': is_teacher,
#                 'token_expires_in': 18000
#             }), 200
#         else:
#             return jsonify({'message': 'Failed to generate token'}), 500
#     except Exception as e:
#         logger.error(f"Token generation error: {e}")
#         return jsonify({'message': 'Failed to generate token'}), 500

# @app.route('/api/livekit/join', methods=['POST'])
# def join_livekit_room():
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         user_id = data.get('user_id')
#         user_name = data.get('user_name', user_id)
#         user_role = data.get('user_role', 'student')
#         if not session_id or not user_id:
#             return jsonify({'message': 'session_id and user_id are required'}), 400
#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             return jsonify({'message': 'Stream not found'}), 404
#         if user_role != 'teacher':
#             enhanced_stream_manager.add_viewer(session_id, f"viewer-{user_id}")
#         is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
#         token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=user_id,
#             participant_name=user_name,
#             is_teacher=is_teacher
#         )
#         if token:
#             return jsonify({
#                 'session_id': session_id,
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': token,
#                 'participant_id': user_id,
#                 'participant_name': user_name,
#                 'is_teacher': is_teacher,
#                 'token_expires_in': 18000
#             }), 200
#         else:
#             return jsonify({'message': 'Failed to generate LiveKit token'}), 500
#     except Exception as e:
#         logger.error(f"Error joining LiveKit room: {e}")
#         return jsonify({'message': 'Failed to join LiveKit room', 'error': str(e)}), 500

# @app.route('/api/enhanced-stream/stop', methods=['POST'])
# def stop_enhanced_stream():
#     try:
#         data = request.get_json()
#         session_id = data.get('session_id')
#         if not session_id:
#             return jsonify({'message': 'session_id is required'}), 400
#         if session_id in enhanced_stream_manager.streams:
#             del enhanced_stream_manager.streams[session_id]
#         loop = asyncio.new_event_loop()
#         asyncio.set_event_loop(loop)
#         loop.run_until_complete(livekit_manager.delete_room(session_id))
#         return jsonify({'message': 'Stream stopped successfully'}), 200
#     except Exception as e:
#         logger.error(f"Stream stop error: {e}")
#         return jsonify({'message': 'Failed to stop stream', 'error': str(e)}), 500

# @app.route('/active-streams', methods=['GET'])
# def get_active_streams():
#     try:
#         active_streams = []
#         for session_id, stream in enhanced_stream_manager.streams.items():
#             if stream['status'] == 'active':
#                 stream_info = {
#                     'session_id': session_id,
#                     'teacher_id': stream['teacher_id'],
#                     'viewer_count': stream['viewer_count'],
#                     'quality': stream['quality'],
#                     'created_at': stream['start_time'].isoformat(),
#                     'uptime': (datetime.now(timezone.utc) - stream['start_time']).total_seconds(),
#                     'features': {
#                         'chat_enabled': stream.get('chat_enabled', True),
#                         'recording_enabled': stream.get('recording_enabled', True),
#                         'screen_sharing': stream.get('screen_sharing', True)
#                     }
#                 }
#                 active_streams.append(stream_info)
#         return jsonify({
#             'success': True,
#             'streams': active_streams,
#             'active_streams': active_streams,
#             'total_count': len(active_streams),
#             'service': 'Enhanced Real-time Streaming',
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }), 200
#     except Exception as e:
#         logger.error(f"Error getting active streams: {e}")
#         return jsonify({'message': 'Failed to get active streams', 'error': str(e)}), 500

# @app.route('/api/enhanced-stream/list', methods=['GET'])
# def list_enhanced_streams():
#     try:
#         streams = []
#         for session_id, stream in enhanced_stream_manager.streams.items():
#             streams.append({
#                 'session_id': session_id,
#                 'teacher_id': stream['teacher_id'],
#                 'quality': stream['quality'],
#                 'status': stream['status'],
#                 'viewer_count': stream['viewer_count'],
#                 'start_time': stream['start_time'].isoformat(),
#                 'room_exists': session_id in livekit_manager.rooms
#             })
#         return jsonify({
#             'streams': streams,
#             'total': len(streams),
#             'timestamp': datetime.now(timezone.utc).isoformat()
#         }), 200
#     except Exception as e:
#         logger.error(f"Error listing streams: {e}")
#         return jsonify({'message': 'Failed to list streams', 'error': str(e)}), 500

# # Socket.IO Events
# @socketio.on('connect')
# def handle_connect():
#     logger.info(f"Client connected: {request.sid}")
#     emit('connected', {'message': 'Connected to enhanced streaming service'})

# @socketio.on('disconnect')
# def handle_disconnect():
#     sid = request.sid
#     logger.info(f"Client disconnected: {sid}")
#     disconnect_reason = request.environ.get('socketio.disconnect_reason', 'unknown')
#     logger.info(f"Disconnect reason for {sid}: {disconnect_reason}")
#     if disconnect_reason != 'client disconnected':
#         logger.warning(f"Unexpected disconnect for {sid}. Reason: {disconnect_reason}. Check for timeouts or server issues.")
#     for session_id in list(enhanced_stream_manager.streams.keys()):
#         if enhanced_stream_manager.remove_viewer(session_id, sid):
#             stream = enhanced_stream_manager.get_stream(session_id)
#             emit('viewer_left', {'viewer_count': stream['viewer_count']}, room=session_id)

# @socketio.on('join_enhanced_stream')
# def handle_join_enhanced_stream(data):
#     try:
#         session_id = data.get('session_id')
#         if not session_id:
#             emit('error', {'message': 'Session ID required'})
#             return
#         join_room(session_id)
#         enhanced_stream_manager.add_viewer(session_id, request.sid)
#         stream = enhanced_stream_manager.get_stream(session_id)
#         if stream:
#             emit('viewer_count', {'viewer_count': stream['viewer_count']})
#             emit('stream_joined', {
#                 'session_id': session_id,
#                 'viewer_count': stream['viewer_count'],
#                 'status': stream['status']
#             })
#             emit('viewer_joined', {
#                 'viewer_count': stream['viewer_count'],
#                 'viewer_id': request.sid
#             }, room=session_id, include_self=False)
#         else:
#             emit('error', {'message': 'Stream not found'})
#     except Exception as e:
#         logger.error(f"Error joining enhanced stream: {e}")
#         emit('error', {'message': 'Failed to join stream'})

# @socketio.on('leave_enhanced_stream')
# def handle_leave_enhanced_stream(data):
#     try:
#         session_id = data.get('session_id')
#         if session_id:
#             leave_room(session_id)
#             enhanced_stream_manager.remove_viewer(session_id, request.sid)
#             stream = enhanced_stream_manager.get_stream(session_id)
#             if stream:
#                 emit('viewer_left', {'viewer_count': stream['viewer_count']}, room=session_id)
#     except Exception as e:
#         logger.error(f"Error leaving enhanced stream: {e}")

# @socketio.on('request_livekit_token')
# def handle_request_livekit_token(data):
#     try:
#         session_id = data.get('session_id')
#         user_id = data.get('user_id')
#         user_name = data.get('user_name', user_id)
#         user_role = data.get('user_role', 'student')
#         if not session_id or not user_id:
#             emit('error', {'message': 'Session ID and User ID required'})
#             return
#         stream = enhanced_stream_manager.get_stream(session_id)
#         if not stream:
#             emit('error', {'message': 'Stream not found'})
#             return
#         is_teacher = (user_role in ['faculty', 'kota_teacher'] or user_id == stream.get('teacher_id'))
#         token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=user_id,
#             participant_name=user_name,
#             is_teacher=is_teacher
#         )
#         if token:
#             emit('livekit_token_generated', {
#                 'session_id': session_id,
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': token,
#                 'participant_id': user_id,
#                 'participant_name': user_name,
#                 'is_teacher': is_teacher,
#                 'token_expires_in': 18000
#             })
#         else:
#             emit('error', {'message': 'Failed to generate LiveKit token'})
#     except Exception as e:
#         logger.error(f"Error generating LiveKit token: {e}")
#         emit('error', {'message': 'Failed to generate token'})

# @socketio.on('start_stream')
# def handle_start_stream(data):
#     try:
#         session_id = data.get('session_id')
#         teacher_id = data.get('teacher_id')
#         teacher_name = data.get('teacher_name', teacher_id)
#         quality = data.get('quality', 'medium')
#         if not session_id or not teacher_id:
#             emit('error', {'message': 'Session ID and Teacher ID required'})
#             return
#         stream = enhanced_stream_manager.create_enhanced_stream(teacher_id, session_id, request.sid, quality)
#         join_room(session_id)
#         teacher_token = livekit_manager.generate_access_token(
#             room_name=session_id,
#             participant_identity=teacher_id,
#             participant_name=teacher_name,
#             is_teacher=True
#         )
#         if teacher_token:
#             emit('stream_started', {
#                 'session_id': session_id,
#                 'livekit_url': LIVEKIT_URL,
#                 'livekit_token': teacher_token,
#                 'quality': quality,
#                 'token_expires_in': 18000
#             })
#         else:
#             emit('error', {'message': 'Failed to generate LiveKit token'})
#     except Exception as e:
#         logger.error(f"Error starting stream: {e}")
#         emit('error', {'message': 'Failed to start stream'})

# @socketio.on('stop_stream')
# def handle_stop_stream(data):
#     try:
#         session_id = data.get('session_id')
#         if not session_id:
#             emit('error', {'message': 'Session ID required'})
#             return
#         if session_id in enhanced_stream_manager.streams:
#             del enhanced_stream_manager.streams[session_id]
#         leave_room(session_id)
#         loop = asyncio.new_event_loop()
#         asyncio.set_event_loop(loop)
#         loop.run_until_complete(livekit_manager.delete_room(session_id))
#         emit('stream_stopped', {'session_id': session_id})
#     except Exception as e:
#         logger.error(f"Error stopping stream: {e}")
#         emit('error', {'message': 'Failed to stop stream'})

# @socketio.on('enhanced_video_frame')
# def handle_enhanced_video_frame(data):
#     try:
#         session_id = data.get('session_id')
#         frame_data = data.get('frame')
#         if session_id and frame_data:
#             emit('video_frame', {'frame': frame_data}, room=session_id, include_self=False)
#     except Exception as e:
#         logger.error(f"Video frame error: {e}")

# # Start the server
# if __name__ == '__main__':
#     print("=" * 70)
#     print(f"🎥 LiveKit URL: {LIVEKIT_URL}")
#     print(f"🔑 LiveKit API Key: {LIVEKIT_API_KEY}")
#     print("🚀 Server starting on port 8012...")
#     print("=" * 70)
#     if not all([LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET]):
#         print("❌ LiveKit configuration missing! Please check your .env file.")
#         print("Required: LIVEKIT_URL, LIVEKIT_API_KEY, LIVEKIT_API_SECRET")
#         exit(1)
#     print("✅ LiveKit configuration validated")
#     print("🎬 Ready to create streaming rooms with LiveKit integration")
#     DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() in ['true', '1', 't']
#     socketio.run(app, host='0.0.0.0', port=8012, debug=DEBUG, allow_unsafe_werkzeug=True)
#working live code without recording
