# Recording Functionality Fixes

## Issues Fixed

### 1. LiveKit API Stop Egress Error
**Problem**: `'str' object has no attribute 'SerializeToString'`
**Solution**: Fixed the `stop_egress` method to use proper `StopEgressRequest` object instead of passing egress_id directly.

```python
# Before (incorrect)
res = await livekit_api.egress.stop_egress(egress_id)

# After (correct)
stop_request = egress.StopEgressRequest(egress_id=egress_id)
res = await livekit_api.egress.stop_egress(stop_request)
```

### 2. LiveKit API List Egress Error
**Problem**: Incorrect parameter passing to `list_egress`
**Solution**: Fixed to use proper `ListEgressRequest` object.

```python
# Before (incorrect)
egress_list = await livekit_api.egress.list_egress(room_name="", egress_id=egress_id)

# After (correct)
list_request = egress.ListEgressRequest(egress_id=egress_id)
egress_response = await livekit_api.egress.list_egress(list_request)
egress_list = egress_response.items if egress_response else []
```

### 3. S3 Upload Verification
**Problem**: Recording files not being found in S3 bucket
**Solution**: Added S3 upload verification and waiting mechanism.

- Added `wait_for_s3_upload()` method to wait for file upload completion
- Added S3 connectivity testing on initialization
- Added better error handling and logging

### 4. Async Event Loop Issues
**Problem**: "Task attached to different loop" errors
**Solution**: Improved async event loop handling with proper cleanup.

```python
try:
    loop.run_until_complete(livekit_manager.delete_room(session_id))
except Exception as e:
    logger.warning(f"Failed to delete LiveKit room {session_id}: {e}")
finally:
    loop.close()
```

## New Features Added

### 1. Recording Status Endpoint
- `/api/recording/status/<session_id>` - Get detailed recording status
- Shows S3 file status, egress status, and recording metadata

### 2. S3 Files Listing Endpoint
- `/api/recording/s3-files` - List all recordings in S3 bucket
- Shows file sizes, CloudFront URLs, and metadata

### 3. Enhanced Logging
- Added comprehensive logging throughout the recording workflow
- Better error messages and debugging information
- S3 connectivity testing on startup

### 4. S3 Utility Methods
- `check_s3_file_exists()` - Check if file exists in S3
- `list_s3_recordings()` - List all recordings in bucket
- `wait_for_s3_upload()` - Wait for upload completion
- `get_egress_status()` - Get LiveKit egress status

## Configuration Verification

The system now verifies:
- ✅ S3 bucket accessibility
- ✅ AWS credentials validity
- ✅ LiveKit API connectivity
- ✅ Recording permissions

## Testing

Use the provided `test_recording.py` script to test the complete workflow:

```bash
python test_recording.py
```

This will:
1. Start a recording session
2. Monitor recording status
3. Stop the recording
4. Verify S3 upload
5. List all recordings

## Expected Workflow

1. **Start Stream**: Recording automatically starts with LiveKit egress
2. **Recording Active**: Video/audio captured and streamed to S3
3. **Stop Stream**: Recording stops and waits for S3 upload completion
4. **Verification**: System confirms file exists in S3 bucket
5. **Database Storage**: Recording metadata stored in PostgreSQL
6. **Access**: CloudFront URLs provided for permanent access

## Key Configuration

Ensure these environment variables are set:
- `LIVEKIT_URL`
- `LIVEKIT_API_KEY` 
- `LIVEKIT_API_SECRET`
- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `AWS_REGION`
- `S3_BUCKET_NAME`
- `CLOUDFRONT_DOMAIN`

## S3 Bucket Structure

Recordings are stored as:
```
recordings/
├── {session-id}/
│   └── {session-id}_{timestamp}.mp4
```

Example: `recordings/06d14de2-8462-4843-bedf-c5b6e341a968/06d14de2-8462-4843-bedf-c5b6e341a968_20250807_112243.mp4`
