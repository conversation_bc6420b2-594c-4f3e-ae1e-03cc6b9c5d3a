#!/usr/bin/env python3
import requests
import json
import time

print('🎬 Testing Recording with S3 force_path_style fix...')

# Start recording
start_data = {
    'session_id': 'test-s3-fix',
    'teacher_id': 'test-teacher',
    'recording_enabled': True,
    'quality': 'high'
}

response = requests.post('http://localhost:8012/api/enhanced-stream/start', json=start_data)
if response.status_code == 200:
    print('✅ Recording started')
    result = response.json()
    recording_info = result.get('recording', {})
    print(f'   Egress ID: {recording_info.get("egress_id")}')
    print(f'   Status: {recording_info.get("status")}')
else:
    print(f'❌ Failed to start: {response.status_code} - {response.text}')
    exit(1)

# Wait 10 seconds
print('⏳ Recording for 10 seconds...')
time.sleep(10)

# Stop recording
response = requests.post('http://localhost:8012/api/enhanced-stream/stop', json={'session_id': 'test-s3-fix'})
if response.status_code == 200:
    result = response.json()
    print('✅ Recording stopped')
    recording = result.get('recording', {})
    print(f'   Status: {recording.get("status")}')
    print(f'   S3 Key: {recording.get("s3_key")}')
    print(f'   CloudFront URL: {recording.get("cloudfront_url")}')
else:
    print(f'❌ Failed to stop: {response.status_code} - {response.text}')

# Check S3 files
print('\n📁 Checking S3 files...')
response = requests.get('http://localhost:8012/api/recording/s3-files')
if response.status_code == 200:
    result = response.json()
    print(f'Found {result.get("total_count", 0)} files in S3:')
    for file_info in result.get('files', []):
        print(f'  📄 {file_info.get("key")} ({file_info.get("size")} bytes)')
else:
    print(f'❌ Failed to list S3 files: {response.status_code}')
