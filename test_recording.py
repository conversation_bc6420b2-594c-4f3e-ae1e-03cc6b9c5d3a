#!/usr/bin/env python3
"""
Test script for recording functionality
"""

import requests
import json
import time
import sys

# Configuration
BASE_URL = "http://localhost:8012"

def test_recording_workflow():
    """Test the complete recording workflow"""
    print("🎬 Testing Recording Functionality")
    print("=" * 50)
    
    # Step 1: Start a stream with recording enabled
    print("\n1. Starting stream with recording...")
    start_data = {
        "session_id": "test-recording-session",
        "teacher_id": "test-teacher",
        "teacher_name": "Test Teacher",
        "recording_enabled": True,
        "quality": "high",
        "layout": "grid"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/enhanced-stream/start", json=start_data)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Stream started successfully")
            print(f"   Session ID: {result.get('session_id')}")
            print(f"   Recording Status: {result.get('recording', {}).get('status')}")
            print(f"   Egress ID: {result.get('recording', {}).get('egress_id')}")
        else:
            print(f"❌ Failed to start stream: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error starting stream: {e}")
        return False
    
    session_id = start_data["session_id"]
    
    # Step 2: Check recording status
    print("\n2. Checking recording status...")
    try:
        response = requests.get(f"{BASE_URL}/api/recording/status/{session_id}")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Recording status retrieved")
            print(f"   Status: {status.get('recording_info', {}).get('status')}")
            print(f"   S3 Key: {status.get('recording_info', {}).get('s3_key')}")
            print(f"   Egress ID: {status.get('recording_info', {}).get('egress_id')}")
        else:
            print(f"⚠️ Could not get recording status: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Error checking recording status: {e}")
    
    # Step 3: Wait a bit to simulate recording
    print("\n3. Simulating recording for 30 seconds...")
    time.sleep(30)
    
    # Step 4: Stop the stream
    print("\n4. Stopping stream...")
    try:
        response = requests.post(f"{BASE_URL}/api/enhanced-stream/stop", json={"session_id": session_id})
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Stream stopped successfully")
            recording_info = result.get('recording', {})
            if recording_info:
                print(f"   Recording Status: {recording_info.get('status')}")
                print(f"   S3 Key: {recording_info.get('s3_key')}")
                print(f"   CloudFront URL: {recording_info.get('cloudfront_url')}")
        else:
            print(f"❌ Failed to stop stream: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error stopping stream: {e}")
        return False
    
    # Step 5: Check final recording status
    print("\n5. Checking final recording status...")
    try:
        response = requests.get(f"{BASE_URL}/api/recording/status/{session_id}")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Final recording status:")
            print(f"   Status: {status.get('recording_info', {}).get('status')}")
            print(f"   S3 Exists: {status.get('s3_status', {}).get('exists')}")
            print(f"   File Size: {status.get('s3_status', {}).get('size')} bytes")
            print(f"   CloudFront URL: {status.get('s3_status', {}).get('cloudfront_url')}")
        else:
            print(f"⚠️ Could not get final recording status: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Error checking final recording status: {e}")
    
    # Step 6: List all S3 recordings
    print("\n6. Listing all S3 recordings...")
    try:
        response = requests.get(f"{BASE_URL}/api/recording/s3-files")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Found {result.get('total_count', 0)} recordings in S3:")
            for file_info in result.get('files', []):
                print(f"   📁 {file_info.get('key')} ({file_info.get('size')} bytes)")
                print(f"      🔗 {file_info.get('cloudfront_url')}")
        else:
            print(f"⚠️ Could not list S3 recordings: {response.status_code}")
    except Exception as e:
        print(f"⚠️ Error listing S3 recordings: {e}")
    
    print("\n🎉 Recording test completed!")
    return True

def test_health_check():
    """Test the health check endpoint"""
    print("\n🏥 Testing Health Check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            health = response.json()
            print(f"✅ Service is healthy")
            print(f"   LiveKit Status: {health.get('livekit', {}).get('status')}")
            print(f"   AWS Status: {health.get('aws', {}).get('status')}")
            print(f"   S3 Bucket: {health.get('aws', {}).get('s3_bucket')}")
            print(f"   Active Recordings: {health.get('active_recordings', 0)}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Recording Functionality Tests")
    
    # Test health first
    if not test_health_check():
        print("❌ Health check failed, aborting tests")
        sys.exit(1)
    
    # Test recording workflow
    if test_recording_workflow():
        print("\n✅ All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
